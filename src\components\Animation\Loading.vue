<style scoped>
.loader {
  width: 16px;
  height: 16px;
  position: relative;
  z-index: 1;
  transform: translateX(-50%);
}

.loader::before,
.loader::after {
  content: '';
  position: absolute;
  width: inherit;
  height: inherit;
  border-radius: 50%;
  mix-blend-mode: multiply;
  animation: rotate92523 2s infinite cubic-bezier(0.77, 0, 0.175, 1);
}

.loader::before {
  background-color: #9d9b9b;
}

.loader::after {
  background-color: #333333;
  animation-delay: 1s;
}

@keyframes rotate92523 {
  0%,
  100% {
    left: 18px;
  }

  25% {
    transform: scale(0.5);
  }

  50% {
    left: 0%;
  }

  75% {
    transform: scale(1);
  }
}
</style>

<template>
  <div class="loader"></div>
</template>
