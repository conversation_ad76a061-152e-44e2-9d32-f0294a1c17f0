<template>
  <div class="CrateContractDialog">
    <el-dialog :title="dialogTitle" :visible="showDialog" width="622px" :close-on-click-modal="false" top="10vh" @open="dialogOpenHandle" @close="close">
      <div>
        <el-form ref="form" :model="formInfo" :rules="rules" label-width="100px">
          <el-form-item label="合同名称:" prop="contractName">
            <el-input v-model="formInfo.contractName" placeholder="请输入合同名称" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="合同编号:">
            <el-input v-model="formInfo.contractCode" placeholder="请输入合同编号" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="选择客户:" class="selectInput" prop="customerId">
            <el-select v-model="formInfo.customerId" placeholder="请选择客户">
              <el-option v-for="item in customerList" :key="item.customerId" :label="item.customerName" :value="item.customerId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="签订日期:" class="datePicker" prop="signingTime">
            <el-date-picker v-model="formInfo.signingTime" type="date" placeholder="选择签订日期" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item v-if="isSales" label="客户联系人:" class="selectInput">
            <el-select v-model="formInfo.contactsId" placeholder="请选择客户联系人" clearable @focus="getAllContacts">
              <el-option v-for="item in contactsList" :key="item.contactsId" :label="item.contactsName" :value="item.contactsId"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="isSales" label="联系方式:">
            <el-input v-model="formInfo.phone" placeholder="请输入联系方式" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item v-if="isSales" label="施工地址:">
            <el-input v-model="formInfo.address" placeholder="请输入施工地址" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item v-if="isSales" label="质保到期日期:" class="datePicker" label-width="110px">
            <el-date-picker v-model="formInfo.expireTime" type="date" placeholder="选择质保到期日期" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="上传初始合同:">
            <el-upload drag :action="action" :headers="header" :data="dataObj" :file-list="fileList" :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
              <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传一个文件、 上传类型: .doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="submit">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { secretCustomeAllCustomer, secretCustomeAllContacts } from '@/api/clientele'
import { contractAdd, contractUpdate, contractLogAdd } from '@/api/contractNew'
import { mapGetters } from 'vuex'

export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      formInfo: {
        contractCode: null,
        contractName: null,
        customerId: null,
        signingTime: null,
        type: 1,
        fileReq: [],
        contactsId: null,
        phone: null,
        address: null,
        expireTime: null
      },
      rules: {
        contractName: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
        customerId: [{ required: true, message: '请选择客户', trigger: 'change', type: 'number' }],
        signingTime: [{ required: true, message: '请选择签订日期', trigger: 'blur' }]
      },
      customerList: [],
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      fileList: [],
      dataObj: {
        fileName: ''
      },
      contactsList: [], // 客户联系人列表
      fileId: null // 用于判断文件的状态
    }
  },
  computed: {
    ...mapGetters(['token', 'organizationId']),
    dialogTitle() {
      return this.formInfo.contractId ? '编辑合同' : '创建合同'
    },
    isSales() {
      return this.organizationId + '' === '56642510' || this.organizationId + '' === '5973411'
    }
  },
  created() {},
  methods: {
    dialogOpenHandle() {
      this.getAllCustomer()
    },
    close() {
      this.formInfo = {
        contractCode: null,
        contractName: null,
        customerId: null,
        signingTime: null,
        type: 1,
        fileReq: [],
        contactsId: null,
        phone: null,
        address: null,
        expireTime: null
      }
      this.fileList = []
      this.fileId = null
      this.$refs['form'].resetFields()
      this.$emit('update:showDialog', false)
    },
    // 查询所有客户
    async getAllCustomer() {
      const { data } = await secretCustomeAllCustomer()
      this.customerList = data
    },
    // 查询所有客户联系人
    async getAllContacts() {
      const { data } = await secretCustomeAllContacts({ customerId: this.formInfo.customerId })
      this.contactsList = data
    },
    // 添加合同- 上传前触发事件
    beforeUpload(file) {
      const time = new Date().getTime()
      this.dataObj.fileName = 'contract/' + '_' + time + file.name
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    // 添加合同- 上传成功事件
    uploadSuccess(response, file, fileList) {
      this.fileList = [file]
    },
    // 添加合同- 上传删除事件
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    submit() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          const list = []
          if (this.fileList.length) {
            this.fileList.forEach((item) => {
              list.push({
                fileName: item.name,
                fileSize: Math.floor(item.size / 1024),
                fileUrl: item.response ? item.response.data[0] : item.url,
                belongType: 18,
                fileId: item.fileId
              })
            })
          }
          this.formInfo.fileReq = list.length ? list[0] : null

          // 判断附件是被修改、还是删除
          if (this.fileId && !this.formInfo.fileReq) {
            await contractLogAdd({ content: '删除合同', contractId: this.formInfo.contractId })
          } else if (this.fileId && !this.formInfo.fileReq.fileId) {
            await contractLogAdd({ content: '修改合同', contractId: this.formInfo.contractId })
          } else if (!this.fileId && this.formInfo.fileReq && this.formInfo.contractId) {
            await contractLogAdd({ content: '上传合同', contractId: this.formInfo.contractId })
          }

          const loading = this.$loading({
            text: '数据保存中，请稍后...',
            background: 'rgba(0,0,0,0.7)'
          })
          if (this.formInfo.contractId) {
            contractUpdate(this.formInfo)
              .then(() => {
                loading.close()
                this.$emit('addSuccess')
                this.close()
                this.$message.success('修改合同成功！')
              })
              .catch(() => {
                loading.close()
              })
          } else {
            contractAdd(this.formInfo)
              .then(() => {
                loading.close()
                this.$emit('addSuccess')
                this.close()
                this.$message.success('创建合同成功！')
              })
              .catch(() => {
                loading.close()
              })
          }
        }
      })
    },
    showEditData(row) {
      this.fileId = row.fileReq?.fileId
      console.log(this.fileId)

      this.getAllContacts()
      // this.getAllCustomer()
      this.formInfo = {
        contractCode: row.contractCode,
        contractName: row.contractName,
        contractId: row.contractId,
        customerId: row.customerId,
        signingTime: row.signingTime,
        type: 1,
        fileReq: row.fileReq
          ? {
              fileName: row.fileReq.fileName,
              fileSize: row.fileReq.fileSize,
              fileUrl: row.fileReq.fileUrl,
              belongType: 18
            }
          : {},
        contactsId: row.constructionDetailDto.contactsId,
        phone: row.constructionDetailDto.phone,
        address: row.constructionDetailDto.address,
        expireTime: row.expireTime
      }
      this.fileList = row.fileReq
        ? [
            {
              name: row.fileReq.fileName,
              size: row.fileReq.fileSize * 1024,
              url: row.fileReq.fileUrl,
              fileId: row.fileReq.fileId
            }
          ]
        : []
    }
  }
}
</script>
<style scoped lang="scss">
.CrateContractDialog {
  ::v-deep {
    .el-dialog {
      max-height: 780px;
      overflow: auto;
      .el-dialog__header {
        border-bottom: 1px solid #eeeeef;
        .el-dialog__title {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: bold;
          font-size: 18px;
          color: #0b1a44;
        }
      }
      .el-dialog__body {
        padding: 24px 0;
        .el-form {
          padding-left: 60px;
          padding-right: 60px;
          .el-form-item__label {
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #0b1a44;
          }
          .selectInput {
            .el-select {
              width: 100%;
              .el-input__inner {
                background: #f5f5f5;
                color: #0b1a44;
              }
            }
          }
          .datePicker {
            .el-input__inner {
              background: #f5f5f5;
              color: #0b1a44;
            }
          }
          .el-upload-dragger {
            width: 88px;
            height: 88px;
            background: url('~@/assets/contractNew/uploadFile.png') no-repeat;
            background-size: cover;
            border: none;
          }
        }
      }
      .el-dialog__footer {
        & > div {
          display: flex;
          justify-content: center;
          .el-button {
            padding: 0;
            width: 114px;
            height: 38px;
            border-radius: 4px;
            &:first-of-type {
              background: #f2f4ff;
              color: #868b9f;
              &:hover {
                color: #868b9f;
                border-color: #f2f4ff;
              }
            }
          }
        }
      }
    }
  }
}
</style>
