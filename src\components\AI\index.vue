<template>
  <div class="agent">
    <el-dialog custom-class="agent-dialog" title="AI助手" center :visible.sync="dialogVisible" top="67px" :show-close="false" width="width">
      <div class="content-view">
        <div class="empty-view">
          <img class="feifei-logo" src="@/assets/agent/feifei_icon.png" alt="" />
          <span>HI！我是飞飞AI</span>
        </div>
      </div>
      <div class="feature-view">
        <div class="agent-modules">
          <span class="module-item">发票助手</span>
        </div>
        <div class="upload-box">
          <ul class="file-list">
            <li v-for="item in fileList" :key="item.name">
              <img src="@/assets/agent/pdf_icon.png" alt="" />
              <div class="item-info">
                <p>{{ item.name }}</p>
                <p>{{ formattingFileSize(item.size) }}</p>
              </div>
              <i class="el-icon-error" @click="removeFile(item)"></i>
            </li>
          </ul>
          <el-upload
            multiple
            accept=".pdf"
            :limit="5"
            :disabled="fileList.length >= 5"
            :show-file-list="false"
            :auto-upload="false"
            :action="`${baseUrl}/ai/invoice/extract`"
            :file-list="fileList"
            :on-exceed="handleExceed"
            :on-change="handleChange"
          >
            <span class="upload_button" slot="trigger">
              <img src="@/assets/agent/upload_icon.png" alt="" />
              上传文件
            </span>
            <img class="send-icon" src="@/assets/agent/send_icon.png" alt="" />
          </el-upload>
        </div>
      </div>

      <!-- <el-button type="primary" @click="exportExcel">导出</el-button> -->
    </el-dialog>
  </div>
</template>
<script>
import { formattingFileSize } from '@/filters'
export default {
  name: '',
  data() {
    return {
      dialogVisible: false,
      baseUrl: window.config.VUE_APP_BASE_API,
      fileList: []
    }
  },
  created() {},
  watch: {
    fileList(val) {
      console.log(val)
    }
  },
  methods: {
    formattingFileSize,
    beforeUpload(file) {
      console.log(file)
    },
    handleChange(file, fileList) {
      console.log(file)

      // 限制大小<10Mb
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件大小不能超过10Mb')
        return false
      }
      const findData = this.fileList.find((item) => item.name === file.name)
      if (findData.name !== file.name) {
        this.fileList.push(file)
      }

      return true
    },
    removeFile(item) {
      this.fileList = this.fileList.filter((file) => file.uid !== item.uid)
    },
    exportExcel() {
      const data = [
        {
          num: '1',
          type: '电子发票（普通发票）',
          invoiceNum: '25377000000414400737',
          date: '2025-08-19',
          servicesName: '*汽油*95号车用汽油(ⅥB 95号',
          saleName: '中国石化销售股份有限公司山东济南石油分公司',
          price: '39.43',
          tax: '5.13',
          countNum: '44.56',
          userName: '王奇'
        },
        {
          num: '2',
          type: '电子发票（普通发票）',
          invoiceNum: '25377000000415169381',
          date: '2025-08-19',
          servicesName: '*汽油*95号车用汽油(ⅥB 95号',
          saleName: '中国石化销售股份有限公司山东济南石油分公司',
          price: '40.81',
          tax: '5.30',
          countNum: '46.11',
          userName: '王奇'
        },
        {
          num: '3',
          type: '电子发票（普通发票）',
          invoiceNum: '25377000000415171978',
          date: '2025-08-19',
          servicesName: '*汽油*95号车用汽油(ⅥB 95号',
          saleName: '中国石化销售股份有限公司山东济南石油分公司',
          price: '42.84',
          tax: '5.57',
          countNum: '48.41',
          userName: '王奇'
        },
        {
          num: '4',
          type: '电子发票（普通发票）',
          invoiceNum: '25362000000082167477',
          date: '2025-08-15',
          servicesName: '*现代服务*信息技术服务',
          saleName: '九江小汐梓信息技术有限公司',
          price: '225.74',
          tax: '2.26',
          countNum: '228.00',
          userName: '王奇'
        }
      ]
      const headers = {
        num: '序号',
        type: '发票类型',
        invoiceNum: '发票号码',
        date: '开票日期',
        servicesName: '货物、劳务或服务名称',
        saleName: '销售方',
        price: '价款',
        tax: '税额',
        countNum: '价税合计',
        userName: '移交人'
      }
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        excel.export_json_to_excel({
          header: Object.values(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '电子发票交接表' // 非必填
        })
      })
    },
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          return item[key]
        })
      })
    }
  }
}
</script>
<style lang="scss">
.agent-dialog {
  position: relative;
  width: 620px;
  height: 800px;
  margin-right: 213px;
  background: #dcebff;
  border-radius: 30px;

  .el-dialog__header {
    padding: 0;
    padding-top: 30px;
    .el-dialog__title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #333333;
    }
  }
  .el-dialog__body {
    padding: 0 40px 0;
  }
  .el-dialog__footer {
    display: none;
  }
  .content-view {
    .empty-view {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 54px;
      .feifei-logo {
        margin-right: 18px;
        width: 101px;
        height: 130px;
      }
      span {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 30px;
        color: #333333;
      }
    }
  }
  .feature-view {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    .agent-modules {
      display: flex;
      align-items: center;
      .module-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 40px;
        margin-right: 20px;
        background: #ffffff;
        border-radius: 80px 80px 80px 80px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #2093fe;
        cursor: pointer;
      }
    }
    .upload-box {
      position: relative;
      width: 560px;
      min-height: 130px;
      margin-top: 10px;
      padding: 20px 13px 89px;
      background: #ffffff;
      border-radius: 20px 20px 20px 20px;
      border: 1px solid #dfdfdf;
      .file-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        li {
          position: relative;
          display: flex;
          align-items: center;
          width: 264px;
          height: 76px;
          margin-right: 4px;
          margin-bottom: 4px;
          background: #fcfcfc;
          border-radius: 14px 14px 14px 14px;
          border: 1px solid #e1e1e1;
          &:nth-of-type(even) {
            margin-right: 0;
          }

          img {
            width: 40px;
            height: 40px;
            margin-right: 5px;
            margin-left: 10px;
          }
          .item-info {
            & > p:first-of-type {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #000000;
            }
            & > p:last-of-type {
              margin-top: 4px;
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 12px;
              color: #999999;
            }
          }
          .el-icon-error {
            position: absolute;
            top: 3px;
            right: 5px;
            font-size: 18px;
            color: #333333;
            cursor: pointer;
          }
        }
      }
      .upload_button {
        position: absolute;
        left: 16px;
        bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 148px;
        height: 60px;
        background: #f5f5f5;
        border-radius: 14px 14px 14px 14px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #000000;
        img {
          margin-right: 5px;
          width: 26px;
          height: 26px;
        }
      }
      .send-icon {
        position: absolute;
        right: 14px;
        bottom: 11px;
        cursor: pointer;
      }
    }
  }
}
</style>
