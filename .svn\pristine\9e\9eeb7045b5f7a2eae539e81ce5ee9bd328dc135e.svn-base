<template>
  <div class="app-container">
    <div class="emulation_content">
      <div class="header">
        <div class="statsBox">
          <!-- <span>未发布:{{ headerStats.noRelease }}</span> -->
          <span>测试区:{{ headerStats.testRelease }}</span>
          <span>定稿区:{{ headerStats.finalizeRelease }}</span>
        </div>
        <span class="addButton" @click="add"></span>
      </div>
      <div v-loading.fullscreen.lock="loading" class="body" element-loading-text="数据加载中" element-loading-background="rgba(0, 0, 0, 0.9)">
        <el-form class="searchForm" :model="queryInfo" label-width="90px" inline>
          <el-form-item label="实验名称:">
            <el-input v-model="queryInfo.name" placeholder="请输入实验名称" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="专 业:">
            <el-select v-model="queryInfo.majorId" placeholder="请选择专业" @focus="getAllMajor" @change="getList">
              <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="产品状态:">
            <el-select v-model="queryInfo.state" placeholder="请选择产品状态" @change="getList">
              <el-option v-for="item in productStates" :key="item.majorId" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="margin-left: 20px">
            <el-button type="primary" size="small" @click="getList('search')">查询</el-button>
            <el-button type="primary" plain size="small" @click="reset">重置</el-button>
            <el-button type="warning" size="small" @click="exportList">导出</el-button>
            <el-button v-if="isManager && isEmulationPerson" type="success" size="small" @click="checkDialog = true">审核</el-button>
          </el-form-item>
          <el-form-item style="float: right">
            <el-tooltip effect="dark" :content="tableType === 1 ? '切换至列表视图' : '切换至卡片视图'" placement="top">
              <div class="IconBox" @click="tableType = tableType === 1 ? 2 : 1">
                <svg-icon v-if="tableType === 1" icon-class="emulationTable"></svg-icon>
                <i v-else class="el-icon-s-grid"></i>
              </div>
            </el-tooltip>
          </el-form-item>
        </el-form>
        <div class="tableData">
          <!-- 卡片格式显示 -->
          <template v-if="tableType === 1">
            <ul class="emulation_list">
              <li v-for="item in list" :key="item.emulationId">
                <div class="emulationType" :style="{ background: item.mode === 1 ? '#577ee5' : '#f38f49' }">{{ item.mode === 1 ? '考核模式' : '学习模式' }}</div>
                <div class="previewImgBox" @click="previewEmulation(item)">
                  <img class="previewImg" :src="item.coverUrl" alt="" />
                </div>
                <div class="textInfo">
                  <div class="emulationName">
                    <div class="textTitle">
                      {{ item.name }}
                    </div>
                    <el-popover placement="right" popper-class="editAnddel" width="150px" trigger="hover">
                      <div v-if="isEmulationPerson" @click.stop="edit(item)">
                        <svg-icon icon-class="edit"></svg-icon>
                        <span>编辑</span>
                      </div>
                      <div v-if="isEmulationPerson" @click.stop="del(item)">
                        <i class="el-icon-delete-solid"></i>
                        <span>删除</span>
                      </div>

                      <div v-if="item.mode === 1" @click.stop="examRecord(item)">
                        <svg-icon icon-class="record"></svg-icon>
                        <span>考核记录</span>
                      </div>
                      <div v-if="item.type == 2" @click.stop="sendRelease(item)">
                        <i class="el-icon-s-promotion"></i>
                        <!-- <svg-icon icon-class="record"></svg-icon> -->
                        <span>发布至测试区</span>
                      </div>
                      <div slot="reference" class="operate">
                        <i class="el-icon-more"></i>
                      </div>
                    </el-popover>
                  </div>
                  <div class="status">
                    <div :class="`type${item.type}`" class="typestatus">{{ item.type === 1 ? '内部测试' : item.type === 2 ? '公司发布' : '' }}</div>
                    <div v-if="item.state" :class="`checkType-${item.state}`" class="checkStatus">{{ item.state | checkStatus }}</div>
                    <div :class="`releaseState-${item.releaseState}`" class="releaseState">{{ item.releaseState | releaseState }}</div>
                  </div>
                  <div class="text">
                    <span>专业: {{ item.majorName }}</span>
                    <span>创建时间:{{ item.createTime }}</span>
                    <!-- <div> -->
                    <!-- <el-tooltip popper-class="emulationTooltip" effect="dark" content="考核记录" placement="top">
                      <svg-icon v-if="item.mode === 1" icon-class="record" @click.stop="examRecord(item)"></svg-icon>
                    </el-tooltip> -->
                    <!-- <el-tooltip popper-class="emulationTooltip" effect="dark" content="赋分模型" placement="top">
                    <svg-icon icon-class="model" @click.stop="openModel(item)"></svg-icon>
                  </el-tooltip> -->
                    <!-- </div> -->
                  </div>
                </div>
              </li>
            </ul>
          </template>
          <!-- 列表格式展示 -->
          <template v-else>
            <el-table class="dataList" :data="list" style="width: 100%" border header-cell-class-name="headerRow">
              <el-table-column prop="name" label="实验名称" width="width" align="center" show-overflow-tooltip>
                <template v-slot="{ row }">
                  <span class="itemName" @click="previewEmulation(row)">{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="实验模式" width="120" align="center">
                <template v-slot="{ row }">
                  <div class="emulationType" :style="{ background: row.mode === 1 ? '#577ee5' : '#f38f49' }">{{ row.mode === 1 ? '考核模式' : '学习模式' }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="实验类型" width="120" align="center">
                <template v-slot="{ row }">
                  <div :class="`type${row.type}`" class="typestatus">{{ row.type === 1 ? '内部测试' : row.type === 2 ? '公司发布' : '' }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="审核状态" width="width" align="center">
                <template v-slot="{ row }">
                  <div :class="`checkType-${row.state}`" class="checkStatus">{{ row.state | checkStatus }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="majorName" label="专业" width="120" align="center"> </el-table-column>
              <el-table-column prop="releaseState" label="产品状态" width="120" align="center">
                <template v-slot="{ row }">
                  <div :class="`releaseState-${row.releaseState}`" class="releaseState">{{ row.releaseState | releaseState }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="version" label="版本" width="150" align="center"> </el-table-column>
              <!-- <el-table-column prop="versionNote" label="版本说明" width="width" align="center"> </el-table-column> -->
              <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
              <el-table-column label="操作" :width="isEmulationPerson ? 680 : 550" align="center">
                <template v-slot="{ row }">
                  <div class="operate">
                    <div v-if="isEmulationPerson" @click.stop="edit(row)">
                      <svg-icon icon-class="edit"></svg-icon>
                      <span>编辑</span>
                    </div>
                    <div v-if="isEmulationPerson" @click.stop="del(row)">
                      <i class="el-icon-delete-solid"></i>
                      <span>删除</span>
                    </div>
                    <div @click.stop="openDataDetails(row, 1)">
                      <i class="el-icon-document"></i>
                      <span>详细信息</span>
                    </div>
                    <div @click.stop="feedbackRecord(row)">
                      <i class="el-icon-time"></i>
                      <span>反馈记录</span>
                    </div>
                    <div @click.stop="editRecord(row)">
                      <i class="el-icon-tickets"></i>
                      <span>修改记录</span>
                    </div>
                    <div v-if="row.type == 2" @click.stop="sendRelease(row)">
                      <i class="el-icon-s-promotion"></i>
                      <span>发布至测试区</span>
                    </div>
                    <div v-if="row.mode === 1" @click.stop="examRecord(row)">
                      <svg-icon icon-class="record"></svg-icon>
                      <span>考核记录</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>

        <div class="paginationBox">
          <div class="pageInfo">第{{ queryInfo.pageNum }}页，共{{ total }}个项目</div>
          <el-pagination
            v-if="list.length > 0"
            layout="total,  prev, pager, next"
            background
            :total="total"
            :page-size.sync="queryInfo.pageSize"
            :current-page.sync="queryInfo.pageNum"
            @size-change="getList"
            @current-change="getList"
          />
        </div>
      </div>
    </div>
    <!-- 删除弹窗 -->
    <el-dialog :visible.sync="delDialog" custom-class="delDialog" width="413px">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该实验吗？</div>
      <div class="operate">
        <span class="closeButton" @click="delDialog = false">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
    <!-- 反馈记录 -->
    <el-dialog title="反馈记录" :visible.sync="feedbackDialog" width="1000px">
      <el-table :data="feedbackList" style="width: 100%" border>
        <el-table-column align="center" prop="createTime" label="创建时间" width="200"> </el-table-column>
        <el-table-column align="center" prop="realName" label="创建人" width="200"> </el-table-column>
        <el-table-column align="center" prop="remark" label="反馈说明" width="width" show-overflow-tooltip> </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="feedbackDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 审核 -->
    <el-dialog title="审核列表" :visible.sync="checkDialog" width="1300px" @open="getCheckList" @close="getList">
      <el-table class="dataList" :data="checkList" style="width: 100%; margin: 0" border header-cell-class-name="headerRow">
        <el-table-column prop="name" label="实验名称" width="width" align="center">
          <template v-slot="{ row }">
            <span class="itemName" @click="previewEmulation(row)">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="实验模式" width="120" align="center">
          <template v-slot="{ row }">
            <div class="emulationType" :style="{ background: row.mode === 1 ? '#577ee5' : '#f38f49' }">{{ row.mode === 1 ? '考核模式' : '学习模式' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="实验类型" width="width" align="center">
          <template v-slot="{ row }">
            <div :class="`type${row.type}`" class="typestatus">{{ row.type === 1 ? '内部测试' : row.type === 2 ? '公司发布' : '' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="operType" label="操作类型" width="120" align="center">
          <template v-slot="{ row }">
            <el-tag :type="row.operType == 1 ? '' : row.operType == 2 ? 'warning' : 'danger'">{{ row.operType == 1 ? '新增' : row.operType == 2 ? '修改' : '删除' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本" width="width" align="center"> </el-table-column>
        <el-table-column prop="realName" label="创建人" width="width" align="center"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
        <el-table-column label="操作" width="250" align="center">
          <template v-slot="{ row }">
            <el-button size="small" type="success " @click="updateCheckStatus(row, 2)">通过</el-button>
            <el-button size="small" type="danger" @click="updateCheckStatus(row, 3)"> 不通过</el-button>
            <el-button size="small" type="primary" @click="openDataDetails(row, 2)"> 详细信息</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" style="text-align: center">
        <el-button type="primary" @click="checkDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 修改记录 -->
    <el-dialog title="修改记录" :visible.sync="editRecordDialog" custom-class="editRecordDialog" width="1000px" @open="editRecordDialogOpen">
      <el-table :data="editRecordList" style="width: 100%" border>
        <el-table-column prop="name" label="实验名称" width="width" align="center">
          <template v-slot="{ row }">
            <span class="itemName" @click="previewEmulation(row)">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="实验模式" width="120" align="center">
          <template v-slot="{ row }">
            <div class="emulationType" :style="{ background: row.mode === 1 ? '#577ee5' : '#f38f49' }">{{ row.mode === 1 ? '考核模式' : '学习模式' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="实验类型" width="width" align="center">
          <template v-slot="{ row }">
            <div :class="`type${row.type}`" class="typestatus">{{ row.type === 1 ? '内部测试' : row.type === 2 ? '公司发布' : '' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="operType" label="操作类型" width="120" align="center">
          <template v-slot="{ row }">
            <el-tag :type="row.operType == 1 ? '' : row.operType == 2 ? 'warning' : 'danger'">{{ row.operType == 1 ? '新增' : row.operType == 2 ? '修改' : '删除' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本" width="width" align="center"> </el-table-column>
        <el-table-column prop="realName" label="创建人" width="width" align="center"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="200" align="center"> </el-table-column>
        <el-table-column label="操作" width="width" align="center">
          <template v-slot="{ row }">
            <el-button size="small" type="primary" @click="openDataDetails(row, 2)"> 详细信息</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="editRecordList.length > 0"
        layout="total, prev, pager, next"
        style="margin-top: 15px; text-align: center"
        background
        :total="editRecordTotal"
        :page-size.sync="editRecordInfo.pageSize"
        :current-page.sync="editRecordInfo.pageNum"
        @size-change="editRecordDialogOpen"
        @current-change="editRecordDialogOpen"
      />
      <div slot="footer">
        <el-button type="primary" @click="editRecordDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 单条数据详情 -->
    <el-dialog title="详细信息" :visible.sync="dataDetailsDialog" custom-class="detailsDialog" top="10vh" width="1000px">
      <el-descriptions v-if="checkedDetails" :column="1" border>
        <el-descriptions-item label="实验名称"> {{ checkedDetails.name }} </el-descriptions-item>
        <el-descriptions-item label="实验模式">
          <div class="emulationType" :style="{ background: checkedDetails.mode === 1 ? '#577ee5' : '#f38f49' }">{{ checkedDetails.mode === 1 ? '考核模式' : '学习模式' }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="实验类型">
          <div :class="`type${checkedDetails.type}`" class="typestatus">{{ checkedDetails.type === 1 ? '内部测试' : checkedDetails.type === 2 ? '公司发布' : '' }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="detailsType === 1" label="产品状态">
          <div :class="`releaseState-${checkedDetails.releaseState}`" class="releaseState">{{ checkedDetails.releaseState | releaseState }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="detailsType === 1" label="审核状态">
          <div :class="`checkType-${checkedDetails.state}`" class="checkStatus">{{ checkedDetails.state | checkStatus }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="detailsType === 2" label="操作类型">
          <el-tag :type="checkedDetails.operType == 1 ? '' : checkedDetails.operType == 2 ? 'warning' : 'danger'">
            {{ checkedDetails.operType == 1 ? '新增' : checkedDetails.operType == 2 ? '修改' : '删除' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="版本"> {{ checkedDetails.version }} </el-descriptions-item>
        <el-descriptions-item label="版本说明"> {{ checkedDetails.versionNote }} </el-descriptions-item>
        <el-descriptions-item v-if="detailsType === 2" label="创建人"> {{ checkedDetails.realName }} </el-descriptions-item>
        <el-descriptions-item label="创建时间"> {{ checkedDetails.createTime }} </el-descriptions-item>
        <el-descriptions-item label="备注"> {{ checkedDetails.remark }} </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button type="primary" @click="dataDetailsDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  emulationList,
  emulationRemove,
  emulationFeedbackList,
  allMajor,
  emulationDetail,
  emulationListExport,
  emulationGetReleaseProductStatis,
  // emulationToReleaseProduct,
  emulationCheckList,
  emulationCheckUpdate,
  emulationRecordList
} from '@/api/emulation'
import { releaseProductAdd } from '@/api/productRelease'
import { formatDate } from '@/filters'
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {
      headerStats: {},
      queryInfo: {
        name: null,
        majorId: null,
        state: null,
        pageNum: 1,
        pageSize: 8
      },
      productStates: [
        {
          label: '测试区',
          value: 2
        },
        {
          label: '定稿区',
          value: 3
        }
      ],
      tableType: 2, // 1 为卡片形式 2 表格形式
      list: [],
      total: 0,
      baseUrl: window.config.VUE_APP_DOWNLOAD_URL,
      majorList: [],
      delDialog: false,
      emulationId: null,
      loading: false,
      feedbackList: [],
      feedbackDialog: false,
      checkDialog: false,
      checkQueryInfo: {
        pageNum: 1,
        pageSize: 8,
        name: null
      },
      checkList: [],
      // 修改记录
      editRecordDialog: false,
      editRecordList: [],
      editRecordTotal: 0,
      editRecordInfo: {
        pageNum: 1,
        pageSize: 8
      },
      // 详细信息
      dataDetailsDialog: false,
      detailsType: 1, // 1为列表查看详情, 2为审核列表详情查看
      checkedDetails: null
    }
  },
  computed: {
    ...mapGetters(['name', 'organizationId', 'token', 'roleName']),
    isEmulationPerson() {
      return this.organizationId + '' === '56648537'
    },
    isManager() {
      return this.roleName?.includes('部门经理')
    }
  },
  created() {
    this.getList()
  },

  methods: {
    async getList(isSearch) {
      this.loading = false

      if (isSearch === 'search') {
        this.queryInfo.pageNum = 1
      }
      const { data } = await emulationList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      this.getReleaseProductStatis()
    },
    reset() {
      const size = this.queryInfo.pageSize
      this.queryInfo = {
        name: null,
        majorId: null,
        pageNum: 1,
        pageSize: size
      }
      this.getList()
    },
    async getAllMajor() {
      const { data } = await allMajor()
      this.majorList = data
    },
    async getReleaseProductStatis() {
      const { data } = await emulationGetReleaseProductStatis()
      this.headerStats = data
      console.log(data)
    },
    add() {
      this.$router.push('/emulation/add/0')
    },
    edit(item) {
      this.$router.push(`/emulation/edit/${item.emulationId}`)
    },
    del(item) {
      this.delDialog = true
      this.emulationId = item.emulationId
    },
    async confirmDel() {
      try {
        await emulationRemove({ id: this.emulationId })
        this.$message.success('操作成功，请等待审核')
        this.delDialog = false
      } catch {
        this.delDialog = false
      }
      this.getList()
    },
    previewEmulation(item) {
      const link = this.$router.resolve(`/previewEmulation/${item.emulationId}/${item.version}`)
      window.open(link.href, '_blank', 'height=1080, width=1920, fullscreen="yes"')
    },
    openModel(item) {
      this.$router.push(`/emulation/model/${item.emulationId}`)
    },
    examRecord(item) {
      if (item.isTest) {
        this.$router.push(`/emulation/examRecord/${item.emulationId}`)
      } else {
        this.$message.error('暂无考核记录')
      }
    },
    async feedbackRecord(item) {
      const { data } = await emulationFeedbackList({ emulationId: item.emulationId })
      this.feedbackList = data
      this.feedbackDialog = true
    },
    downLoadFile(emulationId) {
      return new Promise((resolve, reject) => {
        emulationDetail({ id: emulationId }).then(async ({ data }) => {
          try {
            const filePaths = await window.electronAPI.downLoadAllFile({ files: data.testReqs, folderName: 'emulationFiles' })
            const results = await window.electronAPI.uploadFile({ filePaths, uploadUrl: window.config.VUE_APP_UPLOAD_Library_URL + '/system/upload/file', token: this.token })
            resolve(results)
          } catch (error) {
            this.$message.error('您可能不在内网范围内，请联系管理员')
            console.log(error)
            reject(error)
          }
        })
      })
    },
    async exportList() {
      const RequestData = { name: this.queryInfo.name, majorId: this.queryInfo.majorId }
      const { data } = await emulationListExport(RequestData)
      const Headers = {
        实验名称: 'name',
        版本: 'version',
        实验模式: 'mode',
        专业名称: 'majorName',
        创建时间: 'createTime',
        是否有考核记录: 'isTest',
        实验分类: 'type',
        版本说明: 'versionNote',
        审核状态: 'state'
      }
      const res = await this.formatJson(Headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        excel.export_json_to_excel({
          header: Object.keys(Headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '虚拟仿真列表' // 非必填
        })
      })
    },
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          const ItemKey = headers[key]
          if (ItemKey === 'mode') {
            return item[ItemKey] === 1 ? '考核模式' : '学习模式'
          } else if (ItemKey === 'isTest') {
            return item[ItemKey] === 1 ? '是' : '否'
          } else if (ItemKey === 'type') {
            return item[ItemKey] === 1 ? '内部测试' : '公司发布'
          } else if (ItemKey === 'state') {
            return item[ItemKey] === 1 ? '修改审核中' : item[ItemKey] === 2 ? ' 删除审核中' : item[ItemKey] === 3 ? '审核通过' : '审核未通过'
          }
          return item[ItemKey]
        })
      })
    },
    // 发送至测试区
    async sendRelease(item) {
      if (window.electronAPI) {
        const internals = await window.electronAPI.getLocalIps()
        const isInternal = internals.some((ip) => this.isInternalNetwork(ip))
        if (!isInternal) {
          this.$message.error('外网环境不可执行该操作')
          return false
        }
      }
      this.$confirm('确定要发布到产品库测试区吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const loading = this.$loading({
            text: '操作中,请稍后...',
            background: 'rgba(0,0,0,0.7)'
          })
          try {
            // await emulationToReleaseProduct(item.emulationId)
            const result = await this.downLoadFile(item.emulationId)
            console.log(result)

            const info = {
              name: item.name,
              emulationId: item.emulationId,
              version: item.version,
              versionNote: item.versionNote,
              type: 2,
              state: 1,
              mode: item.mode,
              majorId: item.majorId,
              releaseTime: formatDate(new Date(), 'yyyy-MM-dd'),
              testReqs: result.map((item) => {
                console.log(item)

                return {
                  fileUrl: item.response[0],
                  fileName: item.fileName,
                  belongType: 22
                }
              }),
              productId: null,
              address: null,
              description: null,
              remark: null
            }
            console.log(info)

            await releaseProductAdd(info)
            loading.close()
            this.$message.success('操作成功!')
            this.getList()
          } catch (error) {
            console.log(error)
            loading.close()
          }
        })
        .catch(() => {})
    },
    // 判断网络环境(内外网)
    isInternalNetwork(ip) {
      return ip.startsWith('10.') || (ip.startsWith('172.') && parseInt(ip.split('.')[1]) >= 16 && parseInt(ip.split('.')[1]) <= 31) || ip.startsWith('192.168.')
    },
    // 获取待审核的列表
    async getCheckList() {
      const { data } = await emulationCheckList(this.checkQueryInfo)
      this.checkList = data.list
    },
    // 审核状态修改
    async updateCheckStatus(row, state) {
      const info = {
        emulationVersionId: row.emulationVersionId,
        state,
        emulationId: row.emulationId
      }
      await emulationCheckUpdate(info)
      this.$message.success('操作成功')
      this.getCheckList()
      console.log(row)
    },
    // 修改记录
    editRecord(item) {
      this.emulationId = item.emulationId
      this.editRecordDialog = true
    },
    async editRecordDialogOpen() {
      const { data } = await emulationRecordList({ emulationId: this.emulationId, ...this.editRecordInfo })
      this.editRecordList = data.list
      this.editRecordTotal = data.total
    },
    // 详细信息
    async openDataDetails(row, detailsType) {
      this.detailsType = detailsType
      if (detailsType === 1) {
        const { data } = await emulationDetail({ id: row.emulationId })
        this.checkedDetails = { ...data }
      } else {
        this.checkedDetails = { ...row }
      }
      console.log(this.checkedDetails)

      this.dataDetailsDialog = true
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  height: 100%;
  width: 100%;
  background: #e8eaed;
  padding-top: 20px;
  padding-bottom: 20px;

  $checkStatsColors: (
    // 映射 前面为背景色,后面是文字颜色
    #fff3d2: #f9bd19,

    #d8ebff: #007eff,
    #fadfff: #b45ac6,
    #cbf0d3: #32a63c,
    #ffdbd6: #f35351,
    #fbe6c3: #f38f49
  );
  $releaseStates: transparent, #f8dffd, #cddcff;
  $i: 0; // 初始化计数器
  @each $backround, $color in $checkStatsColors {
    // $index: index($checkStatsColors, $backround); // 获取颜色的索引,不支持映射类型
    .checkType-#{$i} {
      background: $backround !important;
      color: $color !important;
    }
    $i: $i + 1; // 增加计数器
  }
  @each $background in $releaseStates {
    $index: index($releaseStates, $background);
    .releaseState-#{$index} {
      background: $background;
    }
  }

  .emulation_content {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    overflow: hidden;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 72px;
      background: #ffffff;
      padding: 0 30px;
      border-bottom: 2px solid #bcbcbc;
      .statsBox {
        span {
          display: inline-block;
          width: 120px;
          height: 40px;
          border-radius: 4px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #000000;
          line-height: 40px;
          text-align: center;
          margin-right: 10px;
        }
        $colors: #f8dffd, #cddcff;
        @for $i from 1 through length($colors) {
          span:nth-of-type(#{$i}) {
            background: nth($colors, $i);
          }
        }
      }
      .addButton {
        width: 116px;
        height: 42px;
        background: url('~@/assets/emulation/add.png') no-repeat;
        background-size: cover;
        cursor: pointer;
        &:hover {
          background: url('~@/assets/emulation/add_hover.png') no-repeat;
          background-size: cover;
        }
      }
    }
    .body {
      position: relative;
      height: calc(100% - 72px);
      padding-top: 20px;
      width: 100%;
      background: #fff;
      ::v-deep {
        .searchForm {
          margin-left: 32px;
          padding-right: 75px;
          .el-form-item {
            margin-bottom: 0;
            .el-form-item__label {
              font-size: 16px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
              color: #0b1a44;
            }
            .el-input {
              .el-input__inner {
                width: 284px;
                height: 36px;
                background: #efefef;
                border: none;
              }
            }

            .IconBox {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 30px;
              height: 30px;
              background: #efefef;
              border-radius: 4px;
              cursor: pointer;
              .svg-icon,
              i {
                font-size: 20px;
              }
            }
          }
        }
      }
      .tableData {
        display: flex;
        justify-content: center;
        margin-top: 20px;

        .emulation_list {
          display: flex;
          align-content: flex-start;
          flex-wrap: wrap;
          width: 1576px;
          max-width: 1576px;
          li {
            position: relative;
            margin-right: 40px;
            margin-bottom: 22px;
            width: 364px;
            height: 290px;
            background: #f5f6f8;
            // box-shadow: 0px 6px 12px 1px rgba(185, 203, 220, 0.24);
            border-radius: 12px 12px 12px 12px;
            overflow: hidden;
            cursor: pointer;
            &:hover {
              .previewImg {
                transform: scale(1.2);
              }
            }
            &:nth-of-type(4n) {
              margin-right: 0;
            }
            .emulationType {
              display: flex;
              align-items: center;
              justify-content: center;
              position: absolute;
              top: 0;
              left: 0;
              width: 91px;
              height: 27px;
              border-radius: 12px 0;
              z-index: 10;
              font-size: 13px;
              color: #fff;
            }
            .previewImgBox {
              width: 100%;
              height: 167px;
              text-align: center;
              overflow: hidden;
              .previewImg {
                height: 100%;
                width: 100%;
                // object-fit: cover;
                transition: all 0.2s;
              }
            }
            .textInfo {
              padding-left: 14px;
              padding-right: 14px;
              padding-top: 10px;
              .emulationName {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .textTitle {
                  font-size: 16px;
                  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                  font-weight: bold;
                  color: #0b1a44;
                }
                .operate {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 30px;
                  height: 30px;
                  cursor: pointer;
                  &:hover {
                    background: rgba(52, 101, 223, 0.2);
                    border-radius: 4px 4px 4px 4px;
                    i {
                      color: #3465df;
                    }
                  }

                  i {
                    font-size: 16px;
                  }
                }
              }

              .status {
                display: flex;
                margin-top: 5px;
                .typestatus,
                .checkStatus,
                .releaseState {
                  margin-right: 6px;
                  padding: 8px 10px;
                  border-radius: 4px;
                  font-family: Microsoft YaHei, Microsoft YaHei;
                  font-weight: 400;
                  font-size: 14px;
                  text-align: center;
                  color: #333333;
                }
                .type1 {
                  background: #fbe6c3;
                }
                .type2 {
                  background: #d6e1ff;
                }
                .releaseState {
                  color: #333333;
                }
              }
              .text {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 16px;
                font-size: 13px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #666666;
              }
            }
          }
        }
      }

      .paginationBox {
        position: absolute;
        bottom: 15px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 1576px;
        .pageInfo {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #666666;
        }
        .el-pagination {
          text-align: right;
        }
      }
    }
  }
  // 删除弹窗的样式
  ::v-deep {
    .delDialog {
      height: 206px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        margin-top: 10px;
        margin-bottom: 30px;
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .operate {
        display: flex;
        justify-content: center;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
  ::v-deep {
    .dataList,
    .detailsDialog,
    .editRecordDialog {
      margin: 0 30px;
      .headerRow {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #666666;
      }
      .itemName {
        cursor: pointer;
        &:hover {
          color: #3464e0;
          text-decoration: underline;
        }
      }

      .emulationType,
      .typestatus,
      .checkStatus,
      .releaseState {
        width: 70px;
        height: 28px;
        margin: 0 auto;
        border-radius: 4px 4px 4px 4px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        text-align: center;
        line-height: 28px;
      }
      .typestatus {
        color: #333333;
      }
      .type1 {
        background: #fbe6c3;
      }
      .type2 {
        background: #d6e1ff;
      }
      .checkStatus {
        width: 82px;
      }
      .releaseState {
        color: #333333;
      }
      .operate {
        display: flex;
        align-items: center;
        div {
          display: flex;
          align-items: center;
          margin-left: 20px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          cursor: pointer;
          &:hover {
            color: #3464e0;
          }
          & > span {
            margin-left: 4px;
          }
        }
      }
    }

    .detailsDialog {
      margin: 0 auto;
      max-height: 800px;
      overflow: auto;
      .el-descriptions-item__content {
        text-align: center;
        font-size: 18px;
        color: #333;
        white-space: pre;
      }
      .el-descriptions-item__label {
        width: 150px;
        text-align: center;
        font-size: 18px;
      }
    }
    .editRecordDialog {
      margin: 0 auto;
    }
  }
}
</style>
<style lang="scss">
.emulationTooltip {
  background: #0b1a44 !important;
}
.editAnddel {
  padding: 0;
  & > div {
    display: flex;
    align-items: center;
    padding: 10px;
    // padding-left: 30px;
    cursor: pointer;
    &:hover {
      background: #eaebef;
    }
    .svg-icon,
    i {
      margin-right: 10px;
      font-size: 20px;
      color: #333;
      cursor: pointer;
    }
    & > span {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #333;
    }
  }
  .popper__arrow {
    display: none;
  }
}
</style>
