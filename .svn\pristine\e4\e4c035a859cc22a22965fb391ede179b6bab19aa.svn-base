.webgl-content * {
	border: 0;
	margin: 0;
	padding: 0
}

.webgl-content {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	overflow: hidden;
}

.logo {
	display: none !important;
}

.progress {
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	height: 6px;
	width: 100%;
	margin: 130px 0 0 0;
}

.empty {
	float: right;
	width: 100%;
	height: 100%;
	display: inline-block;
	background: #FFFFFF;
}

.full {
	float: left;
	width: 0%;
	height: 100%;
	display: inline-block;
	position: relative;
	background: #1b34b6;
}

.full:after {
	content: ' ';
	position: absolute;
	width: 104px;
	height: 48px;
	right: -10px;
	top: 50%;
	transform: translateY(-50%);
	background: url("progress.png") no-repeat center right;
}

.webgl_footer {
	margin-top: 5px;
	height: 38px;
	line-height: 38px;
	font-family: Helvetica, Verdana, Arial, sans-serif;
	font-size: 18px;
}

.webgl_footlogo,
.webgl_foottitle,
.webgl_footfullscreen {
	height: 100%;
	display: inline-block;
	background: transparent center no-repeat;
}

.webgl_footlogo {
	background-image: url('loading_leftlogo.png');
	width: 204px;
	float: left;
}

.webgl_foottitle {
	margin-right: 10px;
	float: right;
}

.webgl_footfullscreen {
	background-image: url('fullscreen.png');
	width: 38px;
	float: right;
}

.webgl_footuser {
	color: #666666;
	line-height: 38px;
	font-size: 16px;
	float: left;
	margin: 0 0 0 10px;
}


/*=================添加样式=========================*/

.webgl_container {
	background: url('load_back.png') 0 0 no-repeat !important;
	background-size: 100% 100% !important;
}

.webgl_container_before {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 99;
}

.webgl_infobox {
	width: 100%;
	height: 700px;
	max-width: 100%;
	background-size: 100% 100% !important;
	position: absolute;
	left: 0;
	top: 50%;
	margin: -390px 0 0 0;
}

.webgl_infoP1 {
	width: 100%;
	height: 92px;
	position: absolute;
	left: 50%;
	top: 0;
	transform: translateX(-50%);
	box-sizing: border-box;
	line-height: 92px;
	text-align: center;
}

.webgl_infoP2 {
	width: 100%;
	height: 42px;
	line-height: 42px;
	text-align: center;
	max-height: 1200px;
	width: 100%;
	font-size: 42px;
	font-family: Source Han Sans SC;
	font-weight: bold;
	color: #1B34B6;
	position: absolute;
	top: 260px;
	left: 0;
}

.webgl_infoP3 {
	width: 100%;
	height: 20px;
	font-size: 20px;
	font-family: Source Han Sans SC;
	font-weight: 400;
	color: #1B34B6;
	line-height: 20px;
	text-align: center;
	position: absolute;
	left: 0;
	top: 380px;
}

.webgl_infoP4 {
	width: 100%;
	height: 50px;
	position: absolute;
	left: 50%;
	bottom: 0;
	transform: translateX(-50%);
	box-sizing: border-box;
	line-height: 50px;
	text-align: center;
}

.webgl_infotips {
	width: calc(100% - 200px);
	position: absolute;
	left: 100px;
	bottom: 80px;
	font-size: 16px;
	font-weight: 400;
	line-height: 30px;
	color: #B9C1E2;
	word-break: break-all;
}

.webgl_infotips div {
	margin: 10px 0;
}

.textWarning {
	color: #FF8C33;
}

.textSuccess {
	color: #39EE57;
}

/*==================2.2.0========================*/
body {
	padding: 0;
	margin: 0
}

#unity-container {
	position: absolute;
}

#unity-container.unity-desktop {
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%)
}

#unity-container.unity-mobile {
	position: fixed;
	width: 100%;
	height: 100%
}

#unity-canvas {
	background: #FFFFFF;
}

.unity-mobile #unity-canvas {
	width: 100%;
	height: 100%
}

#unity-loading-bar {
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	height: 6px;
	width: 100%;
	margin: 130px 0 0 0;
}

#unity-logo {
	display: none !important;
}

#unity-progress-bar-empty {
	float: right;
	width: 100%;
	height: 100%;
	display: inline-block;
	background: #FFFFFF;
}

#unity-progress-bar-full {
	float: left;
	width: 0%;
	height: 100%;
	display: inline-block;
	position: relative;
	background: #1b34b6;
}

#unity-progress-bar-full:after {
	content: ' ';
	position: absolute;
	width: 104px;
	height: 48px;
	right: -10px;
	top: 50%;
	transform: translateY(-50%);
	background: url("progress.png") no-repeat center right;
}

#unity-footer {
	position: relative;
	height: 38px;
	line-height: 38px;
	font-family: Helvetica, Verdana, Arial, sans-serif;
	font-size: 18px;
}

.unity-mobile #unity-footer {
	display: none
}

#unity-webgl-logo {
	height: 100%;
	display: inline-block;
	background: transparent center no-repeat;
	background-image: url('loading_leftlogo.png');
	width: 204px;
	float: left;
}

#unity-build-title {
	float: right;
	margin-right: 10px;
	line-height: 38px;
	font-family: arial;
	font-size: 18px
}

#unity-fullscreen-button {
	cursor: pointer;
	float: right;
	width: 38px;
	height: 38px;
	background: url('fullscreen.png') no-repeat center
}

#unity-warning {
	position: absolute;
	left: 50%;
	top: 5%;
	transform: translate(-50%);
	background: white;
	padding: 10px;
	display: none
}