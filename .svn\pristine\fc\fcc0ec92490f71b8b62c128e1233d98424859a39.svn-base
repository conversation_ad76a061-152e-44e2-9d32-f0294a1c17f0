<template>
  <div>
    <el-dialog title="添加路由" :close-on-click-modal="false" :visible="addMenuDialog" width="450px" @close="close">
      <el-form ref="form" :model="addForm" :rules="rules" label-width="120px">
        <el-form-item label="上级菜单:">
          <el-select ref="selecteltree" v-model="value">
            <el-option v-for="item in Menns" :key="item.id" :label="item.menuName" :value="item.id" style="display: none" />
            <el-tree
              style="padding-left: 15px"
              :data="Menns"
              node-key="id"
              empty-text="暂无菜单"
              highlight-current
              :expand-on-click-node="false"
              :props="defaultProps"
              current-node-key="id"
              default-expand-all
              @node-click="handleNodeClick"
            />
          </el-select>
          <!-- <el-select v-model="value" placeholder="请选择"> <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option> </el-select></el-form-item> -->
        </el-form-item>
        <el-form-item label="菜单类型:">
          <el-radio-group v-model="addForm.menuType">
            <el-radio label="M">目录</el-radio>
            <el-radio label="C">菜单</el-radio>
            <el-radio label="F">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单名称:" prop="menuName">
          <el-input v-model="addForm.menuName" maxlength="40" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="路由地址:" prop="path">
          <el-input v-model="addForm.path" maxlength="20" placeholder="请输入路由地址" />
        </el-form-item>
        <el-form-item label="权限标识:" prop="permissionKey">
          <el-input v-model="addForm.permissionKey" maxlength="40" placeholder="权限标识" />
        </el-form-item>
        <!-- <el-form-item label="排列序号:" prop="sort">
          <el-input v-model.number="addForm.sort" type="number" min="0" placeholder="排列序号" oninput="if(value.length > 4) value=value.slice(0, 4)"></el-input>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirmOnClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { addSysUser } from '@/api/systemUser'
import { getMenuTreeOfParent, addMenu } from '@/api/menu'
export default {
  name: 'MenuAddDialog',
  props: {
    addMenuDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      addForm: {
        path: null,
        permissionKey: null,
        menuName: null,
        menuType: 'M',
        // sort: null,
        parentId: null
      },
      rules: {
        menuName: [{ required: true, tiggle: 'blur', message: '请输入排序 ' }],
        // sort: [{ type: 'number', required: true, tiggle: 'blur', message: '请输入排序' }, { min: 0, tiggle: 'blur', message: '排序不得小于0', type: 'number' }],
        path: [{ required: true, tiggle: 'blur', message: '请输入路由地址' }],
        permissionKey: [{ required: true, tiggle: 'blur', message: '请输入权限标识' }]
      },
      Menns: [],
      value: '',
      defaultProps: {
        label: 'menuName',
        children: 'children'
      }
    }
  },
  methods: {
    getMenuTreeOfParent() {
      getMenuTreeOfParent().then((res) => {
        this.Menns = res.data
        this.value = this.Menns[0].menuName
        this.addForm.parentId = this.Menns[0].id
        console.log(this.Menns)
      })
    },
    close() {
      this.addForm = {
        path: null,
        permissionKey: null,
        menuName: null,
        menuType: 'M',
        // sort: null,
        parentId: null
      }
      this.$refs['form'].resetFields()
      this.$emit('update:addMenuDialog', false)
    },
    // 点击确认后触发的事件
    async confirmOnClick() {
      this.$refs['form'].validate((val) => {
        if (val) {
          addMenu(this.addForm)
            .then((res) => {
              console.log(res)
              this.$message.success('添加成功')
              this.$emit('addSuccess')
              this.close()
            })
            .catch((err) => {
              console.log(err)
            })
        }
      })
    },
    handleNodeClick(node) {
      console.log(node)
      this.value = node.menuName
      this.addForm.parentId = node.id
      this.$refs.selecteltree.blur()
    }
  }
}
</script>

<style lang="scss" scoped></style>
